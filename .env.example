# Application Settings
PROJECT_NAME="PITAS - Pentesting Team Management System"
PROJECT_VERSION="0.1.0"
DESCRIPTION="A comprehensive platform for managing global pentesting teams"
API_V1_STR="/api/v1"
ENVIRONMENT="development"
DEBUG=true
LOG_LEVEL="INFO"

# Security
SECRET_KEY="your-super-secret-key-change-this-in-production"
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM="HS256"

# Database
POSTGRES_SERVER="localhost"
POSTGRES_USER="pitas"
POSTGRES_PASSWORD="pitas_password"
POSTGRES_DB="pitas_db"
POSTGRES_PORT=5432

# Redis
REDIS_URL="redis://localhost:6379/0"

# Neo4j (for vulnerability correlation)
NEO4J_URI="bolt://localhost:7687"
NEO4J_USER="neo4j"
NEO4J_PASSWORD="neo4j_password"

# InfluxDB (for time-series analytics)
INFLUXDB_URL="http://localhost:8086"
INFLUXDB_TOKEN="your-influxdb-token"
INFLUXDB_ORG="pitas"
INFLUXDB_BUCKET="security_metrics"

# Celery
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# CORS
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:8080,https://localhost:3000,https://localhost:8080"

# External APIs
MITRE_ATTACK_API_URL="https://attack.mitre.org/api"
NVD_API_KEY="your-nvd-api-key"
NIST_API_URL="https://services.nvd.nist.gov/rest/json"

# Monitoring
PROMETHEUS_METRICS_PORT=9090
JAEGER_AGENT_HOST="localhost"
JAEGER_AGENT_PORT=6831

# Email (for notifications)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-email-password"
EMAILS_FROM_EMAIL="<EMAIL>"
EMAILS_FROM_NAME="PITAS System"

# File Storage
UPLOAD_PATH="/tmp/pitas/uploads"
MAX_UPLOAD_SIZE=10485760  # 10MB

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Testing
TESTING=false
TEST_DATABASE_URL="postgresql+asyncpg://test:test@localhost:5432/test_pitas"
