"""Pytest configuration and fixtures."""

import asyncio
from typing import As<PERSON><PERSON>enerator, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from pitas.core.config import Settings, get_settings
from pitas.db.base import Base
from pitas.db.session import get_db
from pitas.main import app


# Test settings
def get_test_settings() -> Settings:
    """Get test settings with overrides."""
    return Settings(
        environment="testing",
        testing=True,
        postgres_server="localhost",
        postgres_user="test",
        postgres_password="test",
        postgres_db="test_pitas",
        secret_key="test-secret-key-for-testing-only",
        redis_url="redis://localhost:6379/15",  # Use different DB for tests
        neo4j_password="test",
        influxdb_token="test-token",
    )


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def async_engine():
    """Create test database engine."""
    settings = get_test_settings()
    engine = create_async_engine(
        str(settings.database_url),
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
    )

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    await engine.dispose()


@pytest.fixture
async def db_session(async_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async with AsyncSession(async_engine, expire_on_commit=False) as session:
        yield session
        await session.rollback()


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """Override database dependency."""
    async def _override_get_db():
        yield db_session

    return _override_get_db


@pytest.fixture
def test_app(override_get_db):
    """Create test FastAPI application."""
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_settings] = get_test_settings
    yield app
    app.dependency_overrides.clear()


@pytest.fixture
def client(test_app) -> TestClient:
    """Create test client."""
    return TestClient(test_app)


@pytest.fixture
async def async_client(test_app) -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


@pytest.fixture
def test_user_data() -> dict:
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "username": "testuser",
        "full_name": "Test User",
        "is_active": True,
        "is_superuser": False,
    }


@pytest.fixture
def test_vulnerability_data() -> dict:
    """Sample vulnerability data for testing."""
    return {
        "cve_id": "CVE-2023-12345",
        "title": "Test Vulnerability",
        "description": "A test vulnerability for unit testing",
        "severity": "HIGH",
        "cvss_score": 8.5,
        "affected_systems": ["test-system-1", "test-system-2"],
        "status": "OPEN",
    }


@pytest.fixture
def test_project_data() -> dict:
    """Sample project data for testing."""
    return {
        "name": "Test Penetration Testing Project",
        "description": "A test project for unit testing",
        "client_name": "Test Client",
        "start_date": "2024-01-01T00:00:00Z",
        "end_date": "2024-01-31T23:59:59Z",
        "status": "ACTIVE",
        "project_type": "EXTERNAL",
    }