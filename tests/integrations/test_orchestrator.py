"""Tests for Security Framework Orchestrator."""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from pitas.integrations.orchestrator import (
    SecurityFrameworkOrchestrator,
    EnrichedVulnerability,
    FrameworkCorrelation
)
from pitas.integrations.mitre import MITReTechnique
from pitas.integrations.cvss import CVSSScore
from pitas.integrations.nist import NISTControlMapping, NISTImplementationTier


@pytest.fixture
def orchestrator():
    """Create orchestrator for testing."""
    return SecurityFrameworkOrchestrator()


@pytest.fixture
def sample_vulnerability_data():
    """Sample vulnerability data for testing."""
    return {
        "id": "CVE-2023-1234",
        "title": "Remote Code Execution Vulnerability",
        "description": "A critical vulnerability allowing remote code execution through process injection",
        "severity": "Critical",
        "cve_id": "CVE-2023-1234",
        "affected_systems": ["web-server", "api-gateway"],
        "discovery_date": "2023-12-01"
    }


@pytest.fixture
def mock_mitre_techniques():
    """Mock MITRE techniques for testing."""
    return [
        MITReTechnique(
            technique_id="T1055",
            name="Process Injection",
            description="Adversaries may inject code into processes",
            tactic="defense-evasion",
            platform=["Windows", "Linux"],
            data_sources=["Process Creation"],
            url="https://attack.mitre.org/techniques/T1055"
        ),
        MITReTechnique(
            technique_id="T1059",
            name="Command and Scripting Interpreter",
            description="Adversaries may abuse command interpreters",
            tactic="execution",
            platform=["Windows", "Linux", "macOS"],
            data_sources=["Command Execution"],
            url="https://attack.mitre.org/techniques/T1059"
        )
    ]


@pytest.fixture
def mock_cvss_score():
    """Mock CVSS score for testing."""
    return CVSSScore(
        base_score=9.8,
        base_severity="Critical",
        vector_string="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N"
    )


@pytest.fixture
def mock_nist_controls():
    """Mock NIST controls for testing."""
    return ["PR.AC-01", "PR.DS-01", "DE.AE-01", "RS.RP-01"]


class TestSecurityFrameworkOrchestrator:
    """Test security framework orchestrator functionality."""
    
    @pytest.mark.asyncio
    async def test_correlate_security_data(
        self,
        orchestrator,
        sample_vulnerability_data,
        mock_mitre_techniques,
        mock_cvss_score,
        mock_nist_controls
    ):
        """Test complete security data correlation."""
        # Mock the individual framework clients
        with patch.object(orchestrator, '_map_mitre_techniques', return_value=mock_mitre_techniques), \
             patch.object(orchestrator, '_calculate_cvss_scores', return_value=mock_cvss_score), \
             patch.object(orchestrator, '_map_nist_controls', return_value=mock_nist_controls):
            
            enriched = await orchestrator.correlate_security_data(sample_vulnerability_data)
            
            assert isinstance(enriched, EnrichedVulnerability)
            assert enriched.vulnerability_id == "CVE-2023-1234"
            assert enriched.title == "Remote Code Execution Vulnerability"
            assert len(enriched.mitre_techniques) == 2
            assert enriched.cvss_scores is not None
            assert len(enriched.nist_controls) == 4
            assert enriched.risk_score > 0.0
            assert enriched.business_impact in ["Low", "Medium", "High", "Critical"]
            assert enriched.remediation_priority in ["Low", "Medium", "High", "Critical"]
            assert isinstance(enriched.enriched_at, datetime)
    
    @pytest.mark.asyncio
    async def test_map_mitre_techniques(self, orchestrator, sample_vulnerability_data, mock_mitre_techniques):
        """Test MITRE technique mapping."""
        with patch.object(orchestrator.mitre_client, 'map_techniques', return_value=mock_mitre_techniques):
            techniques = await orchestrator._map_mitre_techniques(sample_vulnerability_data)
            
            assert len(techniques) == 2
            assert techniques[0].technique_id == "T1055"
            assert techniques[1].technique_id == "T1059"
    
    @pytest.mark.asyncio
    async def test_map_mitre_techniques_error(self, orchestrator, sample_vulnerability_data):
        """Test MITRE technique mapping with error."""
        with patch.object(orchestrator.mitre_client, 'map_techniques', side_effect=Exception("API Error")):
            techniques = await orchestrator._map_mitre_techniques(sample_vulnerability_data)
            
            assert techniques == []  # Should return empty list on error
    
    def test_calculate_mitre_confidence(self, orchestrator, mock_mitre_techniques, sample_vulnerability_data):
        """Test MITRE mapping confidence calculation."""
        confidence = orchestrator._calculate_mitre_confidence(
            mock_mitre_techniques,
            sample_vulnerability_data
        )
        
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.0  # Should have some confidence
    
    def test_calculate_mitre_confidence_no_techniques(self, orchestrator, sample_vulnerability_data):
        """Test confidence calculation with no techniques."""
        confidence = orchestrator._calculate_mitre_confidence([], sample_vulnerability_data)
        assert confidence == 0.0
    
    def test_calculate_cvss_scores(self, orchestrator, sample_vulnerability_data, mock_cvss_score):
        """Test CVSS score calculation."""
        with patch.object(orchestrator.cvss_calculator, 'calculate_score_with_context', return_value=mock_cvss_score):
            score = orchestrator._calculate_cvss_scores(sample_vulnerability_data)
            
            assert score is not None
            assert score.base_score == 9.8
            assert score.base_severity == "Critical"
    
    def test_calculate_cvss_scores_error(self, orchestrator, sample_vulnerability_data):
        """Test CVSS score calculation with error."""
        with patch.object(orchestrator.cvss_calculator, 'calculate_score_with_context', side_effect=Exception("Calc Error")):
            score = orchestrator._calculate_cvss_scores(sample_vulnerability_data)
            
            assert score is None  # Should return None on error
    
    def test_map_nist_controls(self, orchestrator, sample_vulnerability_data, mock_nist_controls):
        """Test NIST control mapping."""
        with patch.object(orchestrator.nist_assessor, 'identify_relevant_controls', return_value=mock_nist_controls):
            controls = orchestrator._map_nist_controls(sample_vulnerability_data)
            
            assert len(controls) == 4
            assert "PR.AC-01" in controls
            assert "DE.AE-01" in controls
    
    def test_map_nist_controls_error(self, orchestrator, sample_vulnerability_data):
        """Test NIST control mapping with error."""
        with patch.object(orchestrator.nist_assessor, 'identify_relevant_controls', side_effect=Exception("NIST Error")):
            controls = orchestrator._map_nist_controls(sample_vulnerability_data)
            
            assert controls == []  # Should return empty list on error
    
    def test_create_nist_mapping(self, orchestrator, mock_nist_controls):
        """Test NIST control mapping creation."""
        mapping = orchestrator._create_nist_mapping("CVE-2023-1234", mock_nist_controls)
        
        assert isinstance(mapping, NISTControlMapping)
        assert mapping.vulnerability_id == "CVE-2023-1234"
        assert mapping.relevant_subcategories == mock_nist_controls
        assert mapping.implementation_tier == NISTImplementationTier.RISK_INFORMED
        assert len(mapping.gaps) > 0
        assert len(mapping.recommendations) > 0
    
    def test_calculate_composite_risk_score(self, orchestrator, mock_cvss_score, mock_mitre_techniques, mock_nist_controls):
        """Test composite risk score calculation."""
        risk_score = orchestrator._calculate_composite_risk_score(
            mock_cvss_score,
            mock_mitre_techniques,
            mock_nist_controls
        )
        
        assert 0.0 <= risk_score <= 10.0
        assert risk_score > 0.0
    
    def test_calculate_composite_risk_score_no_cvss(self, orchestrator, mock_mitre_techniques, mock_nist_controls):
        """Test composite risk score with no CVSS score."""
        risk_score = orchestrator._calculate_composite_risk_score(
            None,
            mock_mitre_techniques,
            mock_nist_controls
        )
        
        assert risk_score == 5.0  # Should use default base score
    
    def test_assess_business_impact(self, orchestrator, sample_vulnerability_data):
        """Test business impact assessment."""
        # Test different risk scores
        assert orchestrator._assess_business_impact(sample_vulnerability_data, 9.5) == "Critical"
        assert orchestrator._assess_business_impact(sample_vulnerability_data, 8.0) == "High"
        assert orchestrator._assess_business_impact(sample_vulnerability_data, 5.0) == "Medium"
        assert orchestrator._assess_business_impact(sample_vulnerability_data, 2.0) == "Low"
    
    def test_determine_remediation_priority(self, orchestrator, mock_mitre_techniques):
        """Test remediation priority determination."""
        # Test critical priority
        priority = orchestrator._determine_remediation_priority(9.5, "Critical", mock_mitre_techniques)
        assert priority == "Critical"
        
        # Test high priority
        priority = orchestrator._determine_remediation_priority(7.5, "High", mock_mitre_techniques)
        assert priority == "High"
        
        # Test medium priority
        priority = orchestrator._determine_remediation_priority(5.0, "Medium", mock_mitre_techniques)
        assert priority == "Medium"
        
        # Test low priority
        priority = orchestrator._determine_remediation_priority(2.0, "Low", mock_mitre_techniques)
        assert priority == "Low"
    
    def test_determine_remediation_priority_critical_techniques(self, orchestrator):
        """Test priority with critical MITRE techniques."""
        critical_techniques = [
            MITReTechnique(
                technique_id="T1190",
                name="Exploit Public-Facing Application",
                description="Initial access technique",
                tactic="initial-access",
                platform=["Windows"],
                data_sources=["Application Log"],
                url="https://attack.mitre.org/techniques/T1190"
            )
        ]
        
        # Should be critical due to initial-access tactic
        priority = orchestrator._determine_remediation_priority(7.0, "Medium", critical_techniques)
        assert priority == "Critical"
    
    @pytest.mark.asyncio
    async def test_correlate_frameworks(self, orchestrator, mock_nist_controls):
        """Test framework correlation."""
        correlation = await orchestrator.correlate_frameworks(
            "T1055",
            "CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N",
            mock_nist_controls
        )
        
        assert isinstance(correlation, FrameworkCorrelation)
        assert correlation.mitre_technique_id == "T1055"
        assert correlation.nist_controls == mock_nist_controls
        assert 0.0 <= correlation.correlation_strength <= 1.0
        assert correlation.correlation_type == "multi-framework"
    
    @pytest.mark.asyncio
    async def test_correlate_frameworks_caching(self, orchestrator, mock_nist_controls):
        """Test framework correlation caching."""
        # First call
        correlation1 = await orchestrator.correlate_frameworks(
            "T1055",
            "CVSS:4.0/AV:N/AC:L",
            mock_nist_controls
        )
        
        # Second call should use cache
        correlation2 = await orchestrator.correlate_frameworks(
            "T1055",
            "CVSS:4.0/AV:N/AC:L",
            mock_nist_controls
        )
        
        assert correlation1 == correlation2
        assert len(orchestrator._correlation_cache) > 0
    
    def test_calculate_correlation_strength(self, orchestrator, mock_nist_controls):
        """Test correlation strength calculation."""
        # Test with good correlation indicators
        strength = orchestrator._calculate_correlation_strength(
            "T1055",
            "CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N",
            mock_nist_controls
        )
        
        assert 0.0 <= strength <= 1.0
        assert strength > 0.5  # Should have good correlation
    
    def test_calculate_correlation_strength_weak(self, orchestrator):
        """Test weak correlation strength."""
        strength = orchestrator._calculate_correlation_strength(
            "T9999",  # Non-standard technique
            "CVSS:4.0/AV:L",  # Short vector
            ["XX.XX-01"]  # Few controls
        )
        
        assert 0.0 <= strength <= 1.0
        assert strength <= 0.7  # Should have weaker correlation
    
    @pytest.mark.asyncio
    async def test_get_framework_statistics(self, orchestrator):
        """Test framework statistics retrieval."""
        mock_techniques = {"T1055": MagicMock(), "T1059": MagicMock()}
        
        with patch.object(orchestrator.mitre_client, 'get_techniques', return_value=mock_techniques):
            stats = await orchestrator.get_framework_statistics()
            
            assert isinstance(stats, dict)
            assert "mitre_techniques_count" in stats
            assert "cvss_version" in stats
            assert "nist_categories_count" in stats
            assert "nist_subcategories_count" in stats
            assert "correlation_cache_size" in stats
            assert "last_updated" in stats
            
            assert stats["mitre_techniques_count"] == 2
            assert stats["cvss_version"] == "4.0"


class TestEnrichedVulnerability:
    """Test enriched vulnerability model."""
    
    def test_enriched_vulnerability_creation(self, mock_mitre_techniques, mock_cvss_score, mock_nist_controls):
        """Test creating enriched vulnerability."""
        nist_mapping = NISTControlMapping(
            vulnerability_id="CVE-2023-1234",
            relevant_subcategories=mock_nist_controls,
            implementation_tier=NISTImplementationTier.RISK_INFORMED,
            gaps=["Test gap"],
            recommendations=["Test recommendation"]
        )
        
        enriched = EnrichedVulnerability(
            vulnerability_id="CVE-2023-1234",
            title="Test Vulnerability",
            description="Test description",
            severity="High",
            mitre_techniques=mock_mitre_techniques,
            mitre_confidence=0.8,
            cvss_scores=mock_cvss_score,
            nist_controls=mock_nist_controls,
            nist_mapping=nist_mapping,
            risk_score=8.5,
            business_impact="High",
            remediation_priority="High"
        )
        
        assert enriched.vulnerability_id == "CVE-2023-1234"
        assert len(enriched.mitre_techniques) == 2
        assert enriched.mitre_confidence == 0.8
        assert enriched.cvss_scores.base_score == 9.8
        assert len(enriched.nist_controls) == 4
        assert enriched.risk_score == 8.5
        assert enriched.business_impact == "High"
        assert enriched.remediation_priority == "High"
        assert isinstance(enriched.enriched_at, datetime)
        assert enriched.enrichment_version == "1.0"


class TestFrameworkCorrelation:
    """Test framework correlation model."""
    
    def test_framework_correlation_creation(self, mock_nist_controls):
        """Test creating framework correlation."""
        correlation = FrameworkCorrelation(
            mitre_technique_id="T1055",
            cvss_vector="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N",
            nist_controls=mock_nist_controls,
            correlation_strength=0.85,
            correlation_type="multi-framework"
        )
        
        assert correlation.mitre_technique_id == "T1055"
        assert correlation.cvss_vector.startswith("CVSS:4.0")
        assert len(correlation.nist_controls) == 4
        assert correlation.correlation_strength == 0.85
        assert correlation.correlation_type == "multi-framework"


@pytest.mark.integration
class TestOrchestratorIntegration:
    """Integration tests for security framework orchestrator."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_vulnerability_analysis(self, orchestrator):
        """Test complete end-to-end vulnerability analysis."""
        vulnerability_data = {
            "id": "CVE-2023-INTEGRATION",
            "title": "Integration Test Vulnerability",
            "description": "A comprehensive vulnerability for testing multi-framework integration including authentication bypass, data exposure, and monitoring evasion",
            "severity": "Critical",
            "affected_systems": ["auth-service", "data-api", "monitoring-dashboard"],
            "discovery_date": "2023-12-01",
            "cve_id": "CVE-2023-INTEGRATION"
        }
        
        # Mock all framework clients to return realistic data
        mock_techniques = [
            MITReTechnique(
                technique_id="T1078",
                name="Valid Accounts",
                description="Adversaries may obtain and abuse credentials",
                tactic="initial-access",
                platform=["Windows", "Linux"],
                data_sources=["Authentication"],
                url="https://attack.mitre.org/techniques/T1078"
            )
        ]
        
        mock_score = CVSSScore(
            base_score=9.1,
            base_severity="Critical",
            vector_string="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N"
        )
        
        mock_controls = ["PR.AC-01", "PR.AC-02", "PR.DS-01", "DE.AE-01", "RS.RP-01"]
        
        with patch.object(orchestrator.mitre_client, 'map_techniques', return_value=mock_techniques), \
             patch.object(orchestrator.cvss_calculator, 'calculate_score_with_context', return_value=mock_score), \
             patch.object(orchestrator.nist_assessor, 'identify_relevant_controls', return_value=mock_controls):
            
            enriched = await orchestrator.correlate_security_data(vulnerability_data)
            
            # Verify comprehensive enrichment
            assert enriched.vulnerability_id == "CVE-2023-INTEGRATION"
            assert enriched.risk_score >= 9.0  # Should be high due to critical CVSS and initial-access technique
            assert enriched.business_impact == "Critical"
            assert enriched.remediation_priority == "Critical"
            assert len(enriched.mitre_techniques) == 1
            assert enriched.mitre_techniques[0].tactic == "initial-access"
            assert enriched.cvss_scores.base_severity == "Critical"
            assert len(enriched.nist_controls) == 5
            assert enriched.mitre_confidence > 0.0
            
            # Verify NIST mapping
            assert enriched.nist_mapping is not None
            assert enriched.nist_mapping.vulnerability_id == "CVE-2023-INTEGRATION"
            assert len(enriched.nist_mapping.relevant_subcategories) == 5
    
    @pytest.mark.asyncio
    async def test_performance_benchmark(self, orchestrator):
        """Test orchestrator performance with multiple vulnerabilities."""
        import time
        
        vulnerabilities = [
            {
                "id": f"PERF-TEST-{i}",
                "title": f"Performance Test Vulnerability {i}",
                "description": "Testing orchestrator performance",
                "severity": "Medium"
            }
            for i in range(10)
        ]
        
        # Mock framework responses for performance
        with patch.object(orchestrator.mitre_client, 'map_techniques', return_value=[]), \
             patch.object(orchestrator.cvss_calculator, 'calculate_score_with_context', return_value=None), \
             patch.object(orchestrator.nist_assessor, 'identify_relevant_controls', return_value=[]):
            
            start_time = time.time()
            
            for vuln in vulnerabilities:
                enriched = await orchestrator.correlate_security_data(vuln)
                assert enriched.vulnerability_id == vuln["id"]
            
            end_time = time.time()
            avg_time = (end_time - start_time) / len(vulnerabilities)
            
            # Should process each vulnerability quickly (< 100ms)
            assert avg_time < 0.1, f"Orchestrator too slow: {avg_time:.4f}s per vulnerability"
