"""Tests for NIST CSF 2.0 integration."""

import pytest
from datetime import datetime

from pitas.integrations.nist import (
    NISTCSFAssessor,
    NISTFunction,
    NISTCategory,
    NISTSubcategory,
    NISTImplementationTier,
    NISTControlMapping,
    NISTAssessmentResult
)


@pytest.fixture
def nist_assessor():
    """Create NIST assessor for testing."""
    return NISTCSFAssessor()


@pytest.fixture
def sample_vulnerability_data():
    """Sample vulnerability data for testing."""
    return {
        "id": "CVE-2023-1234",
        "title": "Authentication Bypass Vulnerability",
        "description": "A vulnerability that allows authentication bypass through credential manipulation",
        "severity": "High"
    }


@pytest.fixture
def sample_organization_data():
    """Sample organization data for testing."""
    return {
        "name": "Test Organization",
        "industry": "Technology",
        "size": "Medium",
        "risk_tolerance": "Medium"
    }


class TestNISTCSFAssessor:
    """Test NIST CSF assessor functionality."""
    
    def test_assessor_initialization(self, nist_assessor):
        """Test NIST assessor initialization."""
        assert len(nist_assessor._categories) > 0
        assert len(nist_assessor._subcategories) > 0
        
        # Check that we have categories for all functions
        functions = set()
        for category in nist_assessor._categories.values():
            functions.add(category.function)
        
        assert NISTFunction.GOVERN in functions
        assert NISTFunction.IDENTIFY in functions
        assert NISTFunction.PROTECT in functions
        assert NISTFunction.DETECT in functions
        assert NISTFunction.RESPOND in functions
        assert NISTFunction.RECOVER in functions
    
    def test_identify_relevant_controls(self, nist_assessor, sample_vulnerability_data):
        """Test identifying relevant NIST controls."""
        controls = nist_assessor.identify_relevant_controls(sample_vulnerability_data)
        
        assert isinstance(controls, list)
        assert len(controls) > 0
        
        # Should identify authentication-related controls
        assert any("PR.AC" in control for control in controls)
    
    def test_identify_controls_data_vulnerability(self, nist_assessor):
        """Test control identification for data-related vulnerability."""
        data_vulnerability = {
            "id": "CVE-2023-5678",
            "title": "Data Encryption Weakness",
            "description": "Vulnerability in data encryption implementation",
            "severity": "Medium"
        }
        
        controls = nist_assessor.identify_relevant_controls(data_vulnerability)
        
        # Should identify data security controls
        assert any("PR.DS" in control for control in controls)
    
    def test_identify_controls_monitoring_vulnerability(self, nist_assessor):
        """Test control identification for monitoring-related vulnerability."""
        monitoring_vulnerability = {
            "id": "CVE-2023-9999",
            "title": "Logging Bypass",
            "description": "Vulnerability allows bypassing security monitoring and logging",
            "severity": "High"
        }
        
        controls = nist_assessor.identify_relevant_controls(monitoring_vulnerability)
        
        # Should identify detection controls
        assert any("DE." in control for control in controls)
    
    def test_assess_function_maturity(self, nist_assessor, sample_organization_data):
        """Test NIST function maturity assessment."""
        assessment = nist_assessor.assess_function_maturity(
            NISTFunction.IDENTIFY,
            sample_organization_data
        )
        
        assert isinstance(assessment, NISTAssessmentResult)
        assert assessment.function == NISTFunction.IDENTIFY
        assert assessment.current_tier in NISTImplementationTier
        assert assessment.target_tier in NISTImplementationTier
        assert 0.0 <= assessment.maturity_score <= 100.0
        assert len(assessment.gaps) > 0
        assert len(assessment.recommendations) > 0
        assert isinstance(assessment.assessed_at, datetime)
    
    def test_assess_all_functions(self, nist_assessor, sample_organization_data):
        """Test assessment of all NIST functions."""
        for function in NISTFunction:
            assessment = nist_assessor.assess_function_maturity(
                function,
                sample_organization_data
            )
            
            assert assessment.function == function
            assert assessment.maturity_score >= 0.0
    
    def test_generate_compliance_report(self, nist_assessor, sample_organization_data):
        """Test compliance report generation."""
        report = nist_assessor.generate_compliance_report(sample_organization_data)
        
        assert isinstance(report, dict)
        assert "assessment_date" in report
        assert "organization" in report
        assert "framework_version" in report
        assert "functions" in report
        assert "overall_maturity" in report
        assert "priority_gaps" in report
        assert "recommendations" in report
        
        # Check organization name
        assert report["organization"] == sample_organization_data["name"]
        assert report["framework_version"] == "NIST CSF 2.0"
        
        # Check functions
        assert len(report["functions"]) == len(NISTFunction)
        for function_key in report["functions"]:
            function_data = report["functions"][function_key]
            assert "name" in function_data
            assert "current_tier" in function_data
            assert "target_tier" in function_data
            assert "maturity_score" in function_data
            assert "gaps" in function_data
            assert "recommendations" in function_data
        
        # Check overall maturity
        assert 0.0 <= report["overall_maturity"] <= 100.0
    
    def test_get_subcategory(self, nist_assessor):
        """Test retrieving specific subcategory."""
        subcategory = nist_assessor.get_subcategory("ID.AM-01")
        
        assert subcategory is not None
        assert subcategory.subcategory_id == "ID.AM-01"
        assert subcategory.category_id == "ID.AM"
        assert "Inventories" in subcategory.name
        assert len(subcategory.implementation_examples) > 0
    
    def test_get_subcategory_not_found(self, nist_assessor):
        """Test retrieving non-existent subcategory."""
        subcategory = nist_assessor.get_subcategory("XX.XX-99")
        assert subcategory is None
    
    def test_get_category(self, nist_assessor):
        """Test retrieving specific category."""
        category = nist_assessor.get_category("ID.AM")
        
        assert category is not None
        assert category.category_id == "ID.AM"
        assert category.function == NISTFunction.IDENTIFY
        assert "Asset Management" in category.name
        assert len(category.subcategories) > 0
    
    def test_get_category_not_found(self, nist_assessor):
        """Test retrieving non-existent category."""
        category = nist_assessor.get_category("XX.XX")
        assert category is None


class TestNISTModels:
    """Test NIST model classes."""
    
    def test_nist_category_creation(self):
        """Test creating NIST category."""
        category = NISTCategory(
            function=NISTFunction.PROTECT,
            category_id="PR.AC",
            name="Identity Management, Authentication and Access Control",
            description="Access to physical and logical assets is limited to authorized users",
            subcategories=["PR.AC-01", "PR.AC-02", "PR.AC-03"]
        )
        
        assert category.function == NISTFunction.PROTECT
        assert category.category_id == "PR.AC"
        assert len(category.subcategories) == 3
    
    def test_nist_subcategory_creation(self):
        """Test creating NIST subcategory."""
        subcategory = NISTSubcategory(
            subcategory_id="PR.AC-01",
            category_id="PR.AC",
            name="Identity Management",
            description="Identities and credentials are issued, managed, verified, revoked, and audited",
            implementation_examples=["IAM systems", "MFA"],
            informative_references=["CIS Controls v8 5"]
        )
        
        assert subcategory.subcategory_id == "PR.AC-01"
        assert subcategory.category_id == "PR.AC"
        assert len(subcategory.implementation_examples) == 2
        assert len(subcategory.informative_references) == 1
    
    def test_nist_control_mapping_creation(self):
        """Test creating NIST control mapping."""
        mapping = NISTControlMapping(
            vulnerability_id="CVE-2023-1234",
            relevant_subcategories=["PR.AC-01", "PR.AC-02"],
            implementation_tier=NISTImplementationTier.RISK_INFORMED,
            gaps=["Missing MFA implementation"],
            recommendations=["Implement multi-factor authentication"]
        )
        
        assert mapping.vulnerability_id == "CVE-2023-1234"
        assert len(mapping.relevant_subcategories) == 2
        assert mapping.implementation_tier == NISTImplementationTier.RISK_INFORMED
        assert isinstance(mapping.mapped_at, datetime)
    
    def test_nist_assessment_result_creation(self):
        """Test creating NIST assessment result."""
        result = NISTAssessmentResult(
            function=NISTFunction.DETECT,
            current_tier=NISTImplementationTier.PARTIAL,
            target_tier=NISTImplementationTier.REPEATABLE,
            maturity_score=45.0,
            gaps=["Incomplete monitoring coverage"],
            recommendations=["Implement comprehensive monitoring"]
        )
        
        assert result.function == NISTFunction.DETECT
        assert result.current_tier == NISTImplementationTier.PARTIAL
        assert result.target_tier == NISTImplementationTier.REPEATABLE
        assert result.maturity_score == 45.0
        assert isinstance(result.assessed_at, datetime)


class TestNISTEnums:
    """Test NIST enumeration classes."""
    
    def test_nist_function_enum(self):
        """Test NIST function enumeration."""
        assert NISTFunction.GOVERN.value == "GV"
        assert NISTFunction.IDENTIFY.value == "ID"
        assert NISTFunction.PROTECT.value == "PR"
        assert NISTFunction.DETECT.value == "DE"
        assert NISTFunction.RESPOND.value == "RS"
        assert NISTFunction.RECOVER.value == "RC"
        
        # Test all functions are present
        functions = list(NISTFunction)
        assert len(functions) == 6
    
    def test_nist_implementation_tier_enum(self):
        """Test NIST implementation tier enumeration."""
        assert NISTImplementationTier.PARTIAL.value == "Partial"
        assert NISTImplementationTier.RISK_INFORMED.value == "Risk Informed"
        assert NISTImplementationTier.REPEATABLE.value == "Repeatable"
        assert NISTImplementationTier.ADAPTIVE.value == "Adaptive"
        
        # Test all tiers are present
        tiers = list(NISTImplementationTier)
        assert len(tiers) == 4


@pytest.mark.integration
class TestNISTIntegration:
    """Integration tests for NIST CSF assessor."""
    
    def test_comprehensive_assessment_workflow(self, nist_assessor):
        """Test complete assessment workflow."""
        # Sample vulnerability requiring comprehensive assessment
        vulnerability = {
            "id": "CVE-2023-COMPREHENSIVE",
            "title": "Multi-Vector Security Vulnerability",
            "description": "Vulnerability affecting authentication, data protection, monitoring, and incident response",
            "severity": "Critical",
            "affected_systems": ["auth-service", "data-store", "monitoring-system"],
            "business_impact": "High"
        }
        
        # Identify relevant controls
        controls = nist_assessor.identify_relevant_controls(vulnerability)
        assert len(controls) > 0
        
        # Assess multiple functions
        assessments = {}
        for function in [NISTFunction.IDENTIFY, NISTFunction.PROTECT, NISTFunction.DETECT]:
            assessment = nist_assessor.assess_function_maturity(
                function,
                {"name": "Test Org", "vulnerability": vulnerability}
            )
            assessments[function] = assessment
            assert assessment.maturity_score >= 0.0
        
        # Generate comprehensive report
        report = nist_assessor.generate_compliance_report({
            "name": "Comprehensive Test Organization",
            "vulnerability_data": vulnerability
        })
        
        assert report["overall_maturity"] >= 0.0
        assert len(report["priority_gaps"]) > 0
        assert len(report["recommendations"]) > 0
    
    def test_control_mapping_accuracy(self, nist_assessor):
        """Test accuracy of control mapping for different vulnerability types."""
        test_cases = [
            {
                "vulnerability": {
                    "description": "SQL injection vulnerability in authentication system",
                    "title": "SQL Injection in Auth"
                },
                "expected_controls": ["PR.AC", "PR.DS"]
            },
            {
                "vulnerability": {
                    "description": "Missing encryption for data at rest",
                    "title": "Unencrypted Data Storage"
                },
                "expected_controls": ["PR.DS"]
            },
            {
                "vulnerability": {
                    "description": "Insufficient logging and monitoring capabilities",
                    "title": "Monitoring Gap"
                },
                "expected_controls": ["DE.AE", "DE.CM"]
            },
            {
                "vulnerability": {
                    "description": "Inadequate incident response procedures",
                    "title": "Incident Response Gap"
                },
                "expected_controls": ["RS.RP", "RS.CO"]
            }
        ]
        
        for test_case in test_cases:
            controls = nist_assessor.identify_relevant_controls(test_case["vulnerability"])
            
            # Check that at least one expected control type is identified
            found_expected = False
            for expected in test_case["expected_controls"]:
                if any(expected in control for control in controls):
                    found_expected = True
                    break
            
            assert found_expected, f"Expected controls {test_case['expected_controls']} not found in {controls}"
    
    def test_maturity_progression(self, nist_assessor):
        """Test maturity assessment progression."""
        # Test different maturity levels
        maturity_scenarios = [
            {
                "name": "Immature Organization",
                "maturity_indicators": {"processes": "ad-hoc", "documentation": "minimal"},
                "expected_tier": NISTImplementationTier.PARTIAL
            },
            {
                "name": "Developing Organization", 
                "maturity_indicators": {"processes": "documented", "risk_management": "basic"},
                "expected_tier": NISTImplementationTier.RISK_INFORMED
            },
            {
                "name": "Mature Organization",
                "maturity_indicators": {"processes": "repeatable", "metrics": "established"},
                "expected_tier": NISTImplementationTier.REPEATABLE
            }
        ]
        
        for scenario in maturity_scenarios:
            assessment = nist_assessor.assess_function_maturity(
                NISTFunction.GOVERN,
                scenario
            )
            
            # Verify assessment reflects scenario characteristics
            assert assessment.current_tier in NISTImplementationTier
            assert 0.0 <= assessment.maturity_score <= 100.0
            assert len(assessment.gaps) > 0
            assert len(assessment.recommendations) > 0
