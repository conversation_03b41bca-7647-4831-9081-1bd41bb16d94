"""Tests for CVSS 4.0 integration."""

import pytest
from pydantic import ValidationError

from pitas.integrations.cvss import (
    CVSSCalculator,
    CVSSBaseMetrics,
    CVSSMetric,
    CVSSScore,
    CVSSThreatMetrics,
    CVSSEnvironmentalMetrics,
    CVSSSupplementalMetrics
)


@pytest.fixture
def cvss_calculator():
    """Create CVSS calculator for testing."""
    return CVSSCalculator()


@pytest.fixture
def sample_base_metrics():
    """Sample CVSS base metrics."""
    return CVSSBaseMetrics(
        attack_vector=CVSSMetric.NETWORK,
        attack_complexity=CVSSMetric.LOW,
        attack_requirements=CVSSMetric.NONE,
        privileges_required=CVSSMetric.NONE_PR,
        user_interaction=CVSSMetric.NONE_UI,
        vuln_confidentiality=CVSSMetric.HIGH_IMPACT,
        vuln_integrity=CVSSMetric.HIGH_IMPACT,
        vuln_availability=CVSSMetric.HIGH_IMPACT,
        subseq_confidentiality=CVSSMetric.NONE_SUB,
        subseq_integrity=CVSSMetric.NONE_SUB,
        subseq_availability=CVSSMetric.NONE_SUB
    )


@pytest.fixture
def sample_vulnerability_data():
    """Sample vulnerability data for testing."""
    return {
        "id": "CVE-2023-1234",
        "title": "Critical Remote Code Execution",
        "description": "A critical vulnerability allowing remote code execution",
        "severity": "Critical"
    }


class TestCVSSCalculator:
    """Test CVSS 4.0 calculator functionality."""
    
    def test_calculator_initialization(self):
        """Test CVSS calculator initialization."""
        calculator = CVSSCalculator()
        assert calculator.version == "4.0"
        
        # Test invalid version
        with pytest.raises(ValueError):
            CVSSCalculator(version="3.1")
    
    def test_calculate_base_score(self, cvss_calculator, sample_base_metrics):
        """Test base score calculation."""
        score = cvss_calculator.calculate_base_score(sample_base_metrics)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 10.0
        assert score > 0  # Should be > 0 for high impact vulnerability
    
    def test_calculate_base_score_no_impact(self, cvss_calculator):
        """Test base score calculation with no impact."""
        no_impact_metrics = CVSSBaseMetrics(
            attack_vector=CVSSMetric.NETWORK,
            attack_complexity=CVSSMetric.LOW,
            attack_requirements=CVSSMetric.NONE,
            privileges_required=CVSSMetric.NONE_PR,
            user_interaction=CVSSMetric.NONE_UI,
            vuln_confidentiality=CVSSMetric.NONE_IMPACT,
            vuln_integrity=CVSSMetric.NONE_IMPACT,
            vuln_availability=CVSSMetric.NONE_IMPACT,
            subseq_confidentiality=CVSSMetric.NONE_SUB,
            subseq_integrity=CVSSMetric.NONE_SUB,
            subseq_availability=CVSSMetric.NONE_SUB
        )
        
        score = cvss_calculator.calculate_base_score(no_impact_metrics)
        assert score == 0.0
    
    def test_get_severity_rating(self, cvss_calculator):
        """Test severity rating determination."""
        assert cvss_calculator.get_severity_rating(0.0) == "None"
        assert cvss_calculator.get_severity_rating(2.5) == "Low"
        assert cvss_calculator.get_severity_rating(5.5) == "Medium"
        assert cvss_calculator.get_severity_rating(8.0) == "High"
        assert cvss_calculator.get_severity_rating(9.5) == "Critical"
    
    def test_calculate_score_with_context(self, cvss_calculator, sample_vulnerability_data):
        """Test complete score calculation with context."""
        score = cvss_calculator.calculate_score_with_context(sample_vulnerability_data)
        
        assert isinstance(score, CVSSScore)
        assert 0.0 <= score.base_score <= 10.0
        assert score.base_severity in ["None", "Low", "Medium", "High", "Critical"]
        assert score.vector_string.startswith("CVSS:4.0")
    
    def test_calculate_score_with_threat_metrics(self, cvss_calculator, sample_vulnerability_data):
        """Test score calculation with threat metrics."""
        threat_metrics = CVSSThreatMetrics(
            exploitability=CVSSMetric.POC,
            remediation_level=CVSSMetric.OFFICIAL_FIX,
            report_confidence=CVSSMetric.CONFIRMED
        )
        
        score = cvss_calculator.calculate_score_with_context(
            sample_vulnerability_data,
            threat_metrics=threat_metrics
        )
        
        assert score.threat_score is not None
        assert score.threat_severity is not None
    
    def test_calculate_score_with_environmental_metrics(self, cvss_calculator, sample_vulnerability_data):
        """Test score calculation with environmental metrics."""
        environmental_metrics = CVSSEnvironmentalMetrics(
            modified_attack_vector=CVSSMetric.LOCAL,
            confidentiality_requirement=CVSSMetric.HIGH_IMPACT,
            integrity_requirement=CVSSMetric.HIGH_IMPACT,
            availability_requirement=CVSSMetric.HIGH_IMPACT
        )
        
        score = cvss_calculator.calculate_score_with_context(
            sample_vulnerability_data,
            environmental_metrics=environmental_metrics
        )
        
        assert score.environmental_score is not None
        assert score.environmental_severity is not None
    
    def test_infer_base_metrics(self, cvss_calculator, sample_vulnerability_data):
        """Test base metrics inference from vulnerability data."""
        metrics = cvss_calculator._infer_base_metrics(sample_vulnerability_data)
        
        assert isinstance(metrics, CVSSBaseMetrics)
        assert metrics.attack_vector == CVSSMetric.NETWORK
        assert metrics.vuln_confidentiality == CVSSMetric.HIGH_IMPACT
    
    def test_generate_vector_string(self, cvss_calculator, sample_base_metrics):
        """Test CVSS vector string generation."""
        vector_string = cvss_calculator._generate_vector_string(sample_base_metrics)
        
        assert vector_string.startswith("CVSS:4.0")
        assert "AV:N" in vector_string  # Network attack vector
        assert "AC:L" in vector_string  # Low complexity
        assert "VC:H" in vector_string  # High confidentiality impact


class TestCVSSMetrics:
    """Test CVSS metric models."""
    
    def test_base_metrics_creation(self):
        """Test creating base metrics."""
        metrics = CVSSBaseMetrics(
            attack_vector=CVSSMetric.NETWORK,
            attack_complexity=CVSSMetric.LOW,
            attack_requirements=CVSSMetric.NONE,
            privileges_required=CVSSMetric.NONE_PR,
            user_interaction=CVSSMetric.NONE_UI,
            vuln_confidentiality=CVSSMetric.HIGH_IMPACT,
            vuln_integrity=CVSSMetric.HIGH_IMPACT,
            vuln_availability=CVSSMetric.HIGH_IMPACT,
            subseq_confidentiality=CVSSMetric.NONE_SUB,
            subseq_integrity=CVSSMetric.NONE_SUB,
            subseq_availability=CVSSMetric.NONE_SUB
        )
        
        assert metrics.attack_vector == CVSSMetric.NETWORK
        assert metrics.vuln_confidentiality == CVSSMetric.HIGH_IMPACT
    
    def test_threat_metrics_defaults(self):
        """Test threat metrics with defaults."""
        metrics = CVSSThreatMetrics()
        
        assert metrics.exploitability == CVSSMetric.NOT_DEFINED
        assert metrics.remediation_level == CVSSMetric.NOT_DEFINED
        assert metrics.report_confidence == CVSSMetric.NOT_DEFINED
    
    def test_environmental_metrics_defaults(self):
        """Test environmental metrics with defaults."""
        metrics = CVSSEnvironmentalMetrics()
        
        assert metrics.modified_attack_vector is None
        assert metrics.confidentiality_requirement == CVSSMetric.HIGH_IMPACT
        assert metrics.integrity_requirement == CVSSMetric.HIGH_IMPACT
        assert metrics.availability_requirement == CVSSMetric.HIGH_IMPACT
    
    def test_supplemental_metrics_optional(self):
        """Test supplemental metrics are optional."""
        metrics = CVSSSupplementalMetrics()
        
        assert metrics.safety is None
        assert metrics.automatable is None
        assert metrics.recovery is None


class TestCVSSScore:
    """Test CVSS score model."""
    
    def test_score_creation(self):
        """Test creating a CVSS score."""
        score = CVSSScore(
            base_score=7.5,
            base_severity="High",
            vector_string="CVSS:4.0/AV:N/AC:L/AT:N/PR:N/UI:N/VC:H/VI:H/VA:H/SC:N/SI:N/SA:N"
        )
        
        assert score.base_score == 7.5
        assert score.base_severity == "High"
        assert score.threat_score is None
        assert score.environmental_score is None
    
    def test_score_validation(self):
        """Test score validation."""
        # Test invalid base score
        with pytest.raises(ValidationError):
            CVSSScore(
                base_score=11.0,  # Invalid: > 10.0
                base_severity="High",
                vector_string="CVSS:4.0/AV:N/AC:L"
            )
        
        # Test negative score
        with pytest.raises(ValidationError):
            CVSSScore(
                base_score=-1.0,  # Invalid: < 0.0
                base_severity="High",
                vector_string="CVSS:4.0/AV:N/AC:L"
            )


class TestCVSSMetricEnum:
    """Test CVSS metric enumeration."""
    
    def test_metric_values(self):
        """Test metric enum values."""
        assert CVSSMetric.NETWORK.value == "N"
        assert CVSSMetric.LOCAL.value == "L"
        assert CVSSMetric.HIGH_IMPACT.value == "H"
        assert CVSSMetric.LOW_IMPACT.value == "L"
        assert CVSSMetric.NONE_IMPACT.value == "N"
    
    def test_metric_comparison(self):
        """Test metric comparison."""
        assert CVSSMetric.NETWORK == CVSSMetric.NETWORK
        assert CVSSMetric.NETWORK != CVSSMetric.LOCAL
        
        # Test string comparison
        assert CVSSMetric.NETWORK.value == "N"
        assert CVSSMetric.LOCAL.value == "L"


@pytest.mark.integration
class TestCVSSIntegration:
    """Integration tests for CVSS calculator."""
    
    def test_end_to_end_calculation(self, cvss_calculator):
        """Test end-to-end CVSS calculation."""
        vulnerability_data = {
            "id": "CVE-2023-9999",
            "title": "Critical SQL Injection",
            "description": "SQL injection vulnerability allowing data exfiltration",
            "severity": "Critical",
            "affected_systems": ["web-app-1", "database-1"],
            "discovery_date": "2023-12-01"
        }
        
        # Calculate with all metric types
        threat_metrics = CVSSThreatMetrics(
            exploitability=CVSSMetric.POC,
            remediation_level=CVSSMetric.WORKAROUND,
            report_confidence=CVSSMetric.CONFIRMED
        )
        
        environmental_metrics = CVSSEnvironmentalMetrics(
            modified_attack_vector=CVSSMetric.ADJACENT,
            confidentiality_requirement=CVSSMetric.HIGH_IMPACT,
            integrity_requirement=CVSSMetric.HIGH_IMPACT,
            availability_requirement=CVSSMetric.LOW_IMPACT
        )
        
        score = cvss_calculator.calculate_score_with_context(
            vulnerability_data,
            environmental_metrics=environmental_metrics,
            threat_metrics=threat_metrics
        )
        
        # Verify complete score
        assert score.base_score > 0
        assert score.threat_score is not None
        assert score.environmental_score is not None
        assert score.base_severity in ["Low", "Medium", "High", "Critical"]
        assert score.vector_string.startswith("CVSS:4.0")
        
        # Verify vector string contains all components
        vector_parts = score.vector_string.split("/")
        assert len(vector_parts) >= 12  # At least base metrics
        assert "CVSS:4.0" in vector_parts
    
    def test_performance_benchmark(self, cvss_calculator):
        """Test CVSS calculation performance."""
        import time
        
        vulnerability_data = {
            "id": "PERF-TEST",
            "title": "Performance Test Vulnerability",
            "description": "Testing calculation performance",
            "severity": "Medium"
        }
        
        # Measure calculation time
        start_time = time.time()
        
        for _ in range(100):
            score = cvss_calculator.calculate_score_with_context(vulnerability_data)
            assert score.base_score >= 0
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        # Should be very fast (< 1ms per calculation)
        assert avg_time < 0.001, f"CVSS calculation too slow: {avg_time:.4f}s"
