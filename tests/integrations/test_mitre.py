"""Tests for MITRE ATT&CK integration."""

import pytest
from unittest.mock import AsyncMock, patch

from pitas.integrations.mitre import MITREAttackClient, MITRETechnique


@pytest.fixture
def mitre_client():
    """Create MITRE client for testing."""
    return MITREAttackClient()


@pytest.fixture
def sample_technique_data():
    """Sample MITRE technique data."""
    return {
        "type": "attack-pattern",
        "id": "attack-pattern--01a5a209-b94c-450b-b7f9-946497d91055",
        "name": "Process Injection",
        "description": "Adversaries may inject code into processes...",
        "kill_chain_phases": [
            {
                "kill_chain_name": "mitre-attack",
                "phase_name": "defense-evasion"
            }
        ],
        "x_mitre_platforms": ["Windows", "Linux", "macOS"],
        "x_mitre_data_sources": [
            {
                "data_source": "Process",
                "data_components": ["Process Creation", "Process Modification"]
            }
        ],
        "external_references": [
            {
                "source_name": "mitre-attack",
                "external_id": "T1055",
                "url": "https://attack.mitre.org/techniques/T1055"
            }
        ]
    }


@pytest.fixture
def sample_vulnerability_data():
    """Sample vulnerability data for testing."""
    return {
        "id": "CVE-2023-1234",
        "title": "Remote Code Execution in Web Application",
        "description": "A vulnerability allows remote code execution through process injection",
        "severity": "High"
    }


class TestMITREAttackClient:
    """Test MITRE ATT&CK client functionality."""
    
    def test_parse_technique(self, mitre_client, sample_technique_data):
        """Test parsing of MITRE technique data."""
        technique = mitre_client._parse_technique(sample_technique_data)
        
        assert isinstance(technique, MITReTechnique)
        assert technique.technique_id == "T1055"
        assert technique.name == "Process Injection"
        assert technique.tactic == "defense-evasion"
        assert "Windows" in technique.platform
        assert "Process Creation" in technique.data_sources
        assert technique.url == "https://attack.mitre.org/techniques/T1055"
    
    @pytest.mark.asyncio
    async def test_get_techniques_cached(self, mitre_client):
        """Test cached technique retrieval."""
        # Mock the cache
        mock_techniques = {
            "T1055": MITReTechnique(
                technique_id="T1055",
                name="Process Injection",
                description="Test description",
                tactic="defense-evasion",
                platform=["Windows"],
                data_sources=["Process"],
                url="https://attack.mitre.org/techniques/T1055"
            )
        }
        
        mitre_client._techniques_cache = mock_techniques
        mitre_client._cache_timestamp = pytest.approx(datetime.utcnow(), abs=1)
        
        techniques = await mitre_client.get_techniques()
        assert len(techniques) == 1
        assert "T1055" in techniques
    
    @pytest.mark.asyncio
    async def test_map_techniques(self, mitre_client, sample_vulnerability_data):
        """Test vulnerability to technique mapping."""
        # Mock the get_techniques method
        mock_techniques = {
            "T1055": MITReTechnique(
                technique_id="T1055",
                name="Process Injection",
                description="Adversaries may inject code into processes",
                tactic="defense-evasion",
                platform=["Windows"],
                data_sources=["Process"],
                url="https://attack.mitre.org/techniques/T1055"
            )
        }
        
        with patch.object(mitre_client, 'get_techniques', return_value=mock_techniques):
            mapped_techniques = await mitre_client.map_techniques(sample_vulnerability_data)
            
            assert len(mapped_techniques) > 0
            assert any(t.technique_id == "T1055" for t in mapped_techniques)
    
    @pytest.mark.asyncio
    async def test_get_technique_by_id(self, mitre_client):
        """Test retrieving specific technique by ID."""
        mock_techniques = {
            "T1055": MITReTechnique(
                technique_id="T1055",
                name="Process Injection",
                description="Test description",
                tactic="defense-evasion",
                platform=["Windows"],
                data_sources=["Process"],
                url="https://attack.mitre.org/techniques/T1055"
            )
        }
        
        with patch.object(mitre_client, 'get_techniques', return_value=mock_techniques):
            technique = await mitre_client.get_technique_by_id("T1055")
            
            assert technique is not None
            assert technique.technique_id == "T1055"
            assert technique.name == "Process Injection"
    
    @pytest.mark.asyncio
    async def test_get_technique_by_id_not_found(self, mitre_client):
        """Test retrieving non-existent technique."""
        with patch.object(mitre_client, 'get_techniques', return_value={}):
            technique = await mitre_client.get_technique_by_id("T9999")
            assert technique is None
    
    @pytest.mark.asyncio
    async def test_search_techniques(self, mitre_client):
        """Test searching techniques by query."""
        mock_techniques = {
            "T1055": MITReTechnique(
                technique_id="T1055",
                name="Process Injection",
                description="Adversaries may inject code into processes",
                tactic="defense-evasion",
                platform=["Windows"],
                data_sources=["Process"],
                url="https://attack.mitre.org/techniques/T1055"
            ),
            "T1059": MITReTechnique(
                technique_id="T1059",
                name="Command and Scripting Interpreter",
                description="Adversaries may abuse command interpreters",
                tactic="execution",
                platform=["Windows", "Linux"],
                data_sources=["Command"],
                url="https://attack.mitre.org/techniques/T1059"
            )
        }
        
        with patch.object(mitre_client, 'get_techniques', return_value=mock_techniques):
            # Search for "process"
            results = await mitre_client.search_techniques("process", limit=5)
            
            assert len(results) > 0
            assert any(t.technique_id == "T1055" for t in results)
    
    def test_calculate_mitre_confidence(self, mitre_client, sample_vulnerability_data):
        """Test MITRE mapping confidence calculation."""
        techniques = [
            MITReTechnique(
                technique_id="T1055",
                name="Process Injection",
                description="Test description",
                tactic="defense-evasion",
                platform=["Windows"],
                data_sources=["Process"],
                url="https://attack.mitre.org/techniques/T1055"
            )
        ]
        
        confidence = mitre_client._calculate_mitre_confidence(techniques, sample_vulnerability_data)
        
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0  # Should have some confidence due to keyword match


@pytest.mark.integration
class TestMITREIntegration:
    """Integration tests for MITRE ATT&CK API."""
    
    @pytest.mark.asyncio
    async def test_fetch_techniques_integration(self, mitre_client):
        """Test actual API call to MITRE ATT&CK (requires network)."""
        # This test requires actual network access
        # Skip if running in CI without network access
        try:
            techniques = await mitre_client._fetch_techniques()
            assert len(techniques) > 0
            
            # Check that we have some well-known techniques
            technique_ids = list(techniques.keys())
            assert any(tid.startswith("T1") for tid in technique_ids)
            
        except Exception as e:
            pytest.skip(f"Network integration test failed: {e}")
    
    @pytest.mark.asyncio
    async def test_cache_behavior_integration(self, mitre_client):
        """Test caching behavior with real data."""
        # First call should fetch from API
        techniques1 = await mitre_client.get_techniques(force_refresh=True)
        
        # Second call should use cache
        techniques2 = await mitre_client.get_techniques(force_refresh=False)
        
        # Should be the same data
        assert len(techniques1) == len(techniques2)
        assert set(techniques1.keys()) == set(techniques2.keys())


class TestMITReTechnique:
    """Test MITRE technique model."""
    
    def test_technique_creation(self):
        """Test creating a MITRE technique."""
        technique = MITReTechnique(
            technique_id="T1055",
            name="Process Injection",
            description="Adversaries may inject code into processes",
            tactic="defense-evasion",
            platform=["Windows", "Linux"],
            data_sources=["Process Creation"],
            url="https://attack.mitre.org/techniques/T1055"
        )
        
        assert technique.technique_id == "T1055"
        assert technique.name == "Process Injection"
        assert "Windows" in technique.platform
        assert technique.tactic == "defense-evasion"
    
    def test_technique_validation(self):
        """Test technique field validation."""
        # Test with minimal required fields
        technique = MITReTechnique(
            technique_id="T1055",
            name="Process Injection",
            description="Test description",
            tactic="defense-evasion",
            url="https://attack.mitre.org/techniques/T1055"
        )
        
        assert technique.platform == []  # Default empty list
        assert technique.data_sources == []  # Default empty list
        assert technique.mitigations == []  # Default empty list
