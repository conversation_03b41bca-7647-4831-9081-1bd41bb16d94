
# FastAPI + PostgreSQL + Pydantic: Robust Application Guide

## Project Structure

```
my_fastapi_app/
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── deploy.yml
├── docs/
│   ├── source/
│   │   ├── conf.py
│   │   ├── index.rst
│   │   ├── api.rst
│   │   └── modules.rst
│   ├── Makefile
│   └── requirements.txt
├── migrations/
│   └── versions/
├── scripts/
│   ├── entrypoint.sh
│   ├── wait-for-postgres.sh
│   └── seed_data.py
├── src/
│   └── my_app/
│       ├── __init__.py
│       ├── main.py
│       ├── api/
│       │   ├── __init__.py
│       │   ├── deps.py
│       │   └── v1/
│       │       ├── __init__.py
│       │       ├── endpoints/
│       │       │   ├── __init__.py
│       │       │   ├── auth.py
│       │       │   ├── users.py
│       │       │   └── health.py
│       │       └── router.py
│       ├── core/
│       │   ├── __init__.py
│       │   ├── config.py
│       │   ├── security.py
│       │   ├── logging.py
│       │   └── exceptions.py
│       ├── db/
│       │   ├── __init__.py
│       │   ├── base.py
│       │   ├── session.py
│       │   └── models/
│       │       ├── __init__.py
│       │       ├── base.py
│       │       └── user.py
│       ├── schemas/
│       │   ├── __init__.py
│       │   ├── base.py
│       │   └── user.py
│       ├── services/
│       │   ├── __init__.py
│       │   ├── base.py
│       │   └── user.py
│       └── utils/
│           ├── __init__.py
│           ├── datetime.py
│           └── validators.py
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_main.py
│   ├── api/
│   │   ├── __init__.py
│   │   └── test_users.py
│   ├── services/
│   │   ├── __init__.py
│   │   └── test_user.py
│   └── utils/
│       ├── __init__.py
│       └── test_validators.py
├── .env.example
├── .gitignore
├── .pre-commit-config.yaml
├── alembic.ini
├── docker-compose.yml
├── Dockerfile
├── pyproject.toml
├── README.md
└── requirements.txt
```

## Configuration Files

### pyproject.toml
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "my-fastapi-app"
version = "0.1.0"
description = "A robust FastAPI application with PostgreSQL"
authors = [{name = "Your Name", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "asyncpg>=0.29.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "httpx>=0.25.2",
    "structlog>=23.2.0",
    "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "ruff>=0.1.6",
    "mypy>=1.7.1",
    "pre-commit>=3.5.0",
    "black>=23.11.0",
    "isort>=5.12.0",
]
docs = [
    "sphinx>=7.2.6",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.25.2",
    "myst-parser>=2.0.0",
]

[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
    "TID", # flake8-tidy-imports
    "Q",   # flake8-quotes
    "FLY", # flynt
    "PERF", # Perflint
    "RUF", # Ruff-specific rules
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
    "W191",  # indentation contains tabs
]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

[tool.ruff.mccabe]
max-complexity = 10

[tool.ruff.isort]
known-first-party = ["my_app"]

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
plugins = ["pydantic.mypy"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
pythonpath = ["src"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
```

### .pre-commit-config.yaml
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.6
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
```

## Core Application Code

### src/my_app/core/config.py
```python
"""Application configuration settings."""

from functools import lru_cache
from typing import Any, Dict, Optional

from pydantic import Field, PostgresDsn, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support.
    
    All settings can be overridden by environment variables with the
    same name but in uppercase.
    """
    
    # API Settings
    api_v1_str: str = "/api/v1"
    project_name: str = "My FastAPI App"
    project_version: str = "0.1.0"
    description: str = "A robust FastAPI application"
    
    # Security
    secret_key: str = Field(..., description="Secret key for JWT encoding")
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"
    
    # Database
    postgres_server: str = Field(..., description="PostgreSQL server host")
    postgres_user: str = Field(..., description="PostgreSQL username")
    postgres_password: str = Field(..., description="PostgreSQL password")
    postgres_db: str = Field(..., description="PostgreSQL database name")
    postgres_port: int = Field(default=5432, description="PostgreSQL port")
    
    # Environment
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=False, description="Debug mode")
    testing: bool = Field(default=False, description="Testing mode")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    
    # CORS
    backend_cors_origins: list[str] = Field(
        default_factory=list,
        description="List of allowed CORS origins"
    )
    
    @validator("backend_cors_origins", pre=True)
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    @property
    def database_url(self) -> PostgresDsn:
        """Construct database URL from individual components."""
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            username=self.postgres_user,
            password=self.postgres_password,
            host=self.postgres_server,
            port=self.postgres_port,
            path=f"/{self.postgres_db}",
        )
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance.
    
    Returns:
        Settings: Application settings instance.
    """
    return Settings()


# Global settings instance
settings = get_settings()
```

### src/my_app/db/base.py
```python
"""Database base models and utilities."""

from datetime import datetime
from typing import Any, Optional
from uuid import UUID, uuid4

from sqlalchemy import DateTime, func
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.ext.declarative import as_declarative, declared_attr
from sqlalchemy.orm import Mapped, mapped_column


@as_declarative()
class Base:
    """Base class for all database models.
    
    Provides common fields and functionality for all models.
    """
    
    id: Mapped[UUID] = mapped_column(
        PG_UUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        index=True,
        doc="Unique identifier for the record"
    )
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="Timestamp when the record was created"
    )
    
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        onupdate=func.now(),
        doc="Timestamp when the record was last updated"
    )
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name."""
        return cls.__name__.lower()
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"


# Re-export for convenience
__all__ = ["Base"]
```

### src/my_app/db/session.py
```python
"""Database session management."""

from typing import AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import NullPool

from my_app.core.config import settings

# Create async engine
engine = create_async_engine(
    str(settings.database_url),
    echo=settings.debug,
    poolclass=NullPool if settings.testing else None,
    pool_pre_ping=True,
)

# Create session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get database session.
    
    Yields:
        AsyncSession: Database session instance.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
```

### src/my_app/schemas/base.py
```python
"""Base Pydantic schemas."""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class BaseSchema(BaseModel):
    """Base schema with common configuration.
    
    Provides standard configuration for all Pydantic models.
    """
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True,
        use_enum_values=True,
    )


class TimestampMixin(BaseSchema):
    """Mixin for models with timestamp fields."""
    
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class IDMixin(BaseSchema):
    """Mixin for models with UUID primary key."""
    
    id: UUID = Field(..., description="Unique identifier")


class BaseDBSchema(BaseSchema, IDMixin, TimestampMixin):
    """Base schema for database models with ID and timestamps."""
    pass


class PaginationParams(BaseSchema):
    """Parameters for pagination."""
    
    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Maximum number of records to return")


class PaginatedResponse(BaseSchema):
    """Response schema for paginated results."""
    
    items: list[Any] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    skip: int = Field(..., description="Number of items skipped")
    limit: int = Field(..., description="Maximum number of items returned")
    
    @property
    def has_next(self) -> bool:
        """Check if there are more items available."""
        return self.skip + self.limit < self.total
    
    @property
    def has_previous(self) -> bool:
        """Check if there are previous items available."""
        return self.skip > 0
```

### src/my_app/services/base.py
```python
"""Base service class with common CRUD operations."""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from uuid import UUID

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from my_app.db.base import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """Base service class providing common CRUD operations.
    
    Args:
        model: SQLAlchemy model class
    """
    
    def __init__(self, model: Type[ModelType]) -> None:
        """Initialize the service with a model.
        
        Args:
            model: SQLAlchemy model class
        """
        self.model = model
    
    async def get(
        self,
        db: AsyncSession,
        id: UUID,
    ) -> Optional[ModelType]:
        """Get a single record by ID.
        
        Args:
            db: Database session
            id: Record ID
            
        Returns:
            Model instance or None if not found
        """
        result = await db.execute(select(self.model).where(self.model.id == id))
        return result.scalar_one_or_none()
    
    async def get_multi(
        self,
        db: AsyncSession,
        *,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[List[ModelType], int]:
        """Get multiple records with pagination.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            Tuple of (records, total_count)
        """
        # Get total count
        count_result = await db.execute(select(func.count()).select_from(self.model))
        total = count_result.scalar()
        
        # Get records
        result = await db.execute(
            select(self.model)
            .offset(skip)
            .limit(limit)
        )
        records = result.scalars().all()
        
        return list(records), total
    
    async def create(
        self,
        db: AsyncSession,
        *,
        obj_in: CreateSchemaType,
    ) -> ModelType:
        """Create a new record.
        
        Args:
            db: Database session
            obj_in: Input data for creation
            
        Returns:
            Created model instance
        """
        obj_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_data)
        db.add(db_obj)
        await db.flush()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]],
    ) -> ModelType:
        """Update an existing record.
        
        Args:
            db: Database session
            db_obj: Existing model instance
            obj_in: Update data
            
        Returns:
            Updated model instance
        """
        obj_data = jsonable_encoder(db_obj)
        
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        
        db.add(db_obj)
        await db.flush()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, *, id: UUID) -> Optional[ModelType]:
        """Remove a record by ID.
        
        Args:
            db: Database session
            id: Record ID
            
        Returns:
            Removed model instance or None if not found
        """
        obj = await self.get(db, id=id)
        if obj:
            await db.delete(obj)
            await db.flush()
        return obj
```

### src/my_app/core/exceptions.py
```python
"""Custom application exceptions."""

from typing import Any, Dict, Optional

from fastapi import HTTPException, status


class AppException(Exception):
    """Base application exception.
    
    Args:
        message: Error message
        details: Additional error details
    """
    
    def __init__(
        self,
        message: str,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Initialize the exception.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ValidationError(AppException):
    """Validation error exception."""
    pass


class NotFoundError(AppException):
    """Resource not found exception."""
    pass


class UnauthorizedError(AppException):
    """Unauthorized access exception."""
    pass


class ForbiddenError(AppException):
    """Forbidden access exception."""
    pass


class ConflictError(AppException):
    """Resource conflict exception."""
    pass


# HTTP Exception mapping
def create_http_exception(
    status_code: int,
    message: str,
    details: Optional[Dict[str, Any]] = None,
) -> HTTPException:
    """Create an HTTPException with consistent format.
    
    Args:
        status_code: HTTP status code
        message: Error message
        details: Additional error details
        
    Returns:
        HTTPException instance
    """
    content = {"message": message}
    if details:
        content["details"] = details
    
    return HTTPException(
        status_code=status_code,
        detail=content,
    )


def not_found_exception(message: str = "Resource not found") -> HTTPException:
    """Create a 404 Not Found exception."""
    return create_http_exception(status.HTTP_404_NOT_FOUND, message)


def validation_exception(
    message: str = "Validation error",
    details: Optional[Dict[str, Any]] = None,
) -> HTTPException:
    """Create a 422 Validation Error exception."""
    return create_http_exception(
        status.HTTP_422_UNPROCESSABLE_ENTITY,
        message,
        details,
    )


def unauthorized_exception(message: str = "Unauthorized") -> HTTPException:
    """Create a 401 Unauthorized exception."""
    return create_http_exception(status.HTTP_401_UNAUTHORIZED, message)


def forbidden_exception(message: str = "Forbidden") -> HTTPException:
    """Create a 403 Forbidden exception."""
    return create_http_exception(status.HTTP_403_FORBIDDEN, message)


def conflict_exception(message: str = "Resource conflict") -> HTTPException:
    """Create a 409 Conflict exception."""
    return create_http_exception(status.HTTP_409_CONFLICT, message)
```

### src/my_app/main.py
```python
"""Main FastAPI application module."""

import structlog
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from my_app.api.v1.router import api_router
from my_app.core.config import settings
from my_app.core.exceptions import AppException
from my_app.core.logging import setup_logging


# Configure structured logging
setup_logging()
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan events.
    
    Args:
        app: FastAPI application instance
        
    Yields:
        None: During application runtime
    """
    # Startup
    logger.info("Application starting up", version=settings.project_version)
    
    yield
    
    # Shutdown
    logger.info("Application shutting down")


def create_app() -> FastAPI:
    """Create and configure FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    app = FastAPI(
        title=settings.project_name,
        version=settings.project_version,
        description=settings.description,
        openapi_url=f"{settings.api_v1_str}/openapi.json",
        docs_url=f"{settings.api_v1_str}/docs",
        redoc_url=f"{settings.api_v1_str}/redoc",
        lifespan=lifespan,
    )
    
    # Set up CORS
    if settings.backend_cors_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.backend_cors_origins],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Include routers
    app.include_router(api_router, prefix=settings.api_v1_str)
    
    # Global exception handler
    @app.exception_handler(AppException)
    async def app_exception_handler(
        request: Request,
        exc: AppException,
    ) -> JSONResponse:
        """Handle application exceptions.
        
        Args:
            request: HTTP request
            exc: Application exception
            
        Returns:
            JSONResponse with error details
        """
        logger.error(
            "Application exception occurred",
            exception=exc.__class__.__name__,
            message=exc.message,
            details=exc.details,
            path=request.url.path,
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "message": exc.message,
                "details": exc.details,
            },
        )
    
    return app


# Create application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "my_app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
```

## Testing Configuration

### tests/conftest.py
```python
"""Pytest configuration and fixtures."""

import asyncio
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool

from my_app.core.config import Settings, get_settings
from my_app.db.base import Base
from my_app.db.session import get_db
from my_app.main import app


# Test settings
def get_test_settings() -> Settings:
    """Get test settings with overrides."""
    return Settings(
        environment="testing",
        testing=True,
        postgres_server="localhost",
        postgres_user="test",
        postgres_password="test",
        postgres_db="test_db",
        secret_key="test-secret-key",
    )


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def async_engine():
    """Create test database engine."""
    settings = get_test_settings()
    engine = create_async_engine(
        str(settings.database_url),
        echo=False,
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
    )
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture
async def db_session(async_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create test database session."""
    async with AsyncSession(async_engine, expire_on_commit=False) as session:
        yield session
        await session.rollback()


@pytest.fixture
def override_get_db(db_session: AsyncSession):
    """Override database dependency."""
    async def _override_get_db():
        yield db_session
    
    return _override_get_db


@pytest.fixture
def test_app(override_get_db):
    """Create test FastAPI application."""
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_settings] = get_test_settings
    yield app
    app.dependency_overrides.clear()


@pytest.fixture
def client(test_app) -> TestClient:
    """Create test client."""
    return TestClient(test_app)


@pytest.fixture
async def async_client(test_app) -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client
```

## Documentation Setup (Sphinx)

### docs/source/conf.py
```python
"""Sphinx configuration file."""

import os
import sys
from pathlib import Path

# Add source to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

# Project information
project = "My FastAPI App"
copyright = "2024, Your Name"
author = "Your Name"
release = "0.1.0"

# Extensions
extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.napoleon",
    "sphinx.ext.viewcode",
    "sphinx.ext.intersphinx",
    "sphinx_autodoc_typehints",
    "myst_parser",
]

# Templates path
templates_path = ["_templates"]

# Exclude patterns
exclude_patterns = []

# HTML theme
html_theme = "sphinx_rtd_theme"
html_static_path = ["_static"]

# Napoleon settings
napoleon_google_docstring = True
napoleon_numpy_docstring = False
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False

# Autodoc settings
autodoc_typehints = "description"
autodoc_member_order = "bysource"

# Intersphinx mapping
intersphinx_mapping = {
    "python": ("https://docs.python.org/3", None),
    "fastapi": ("https://fastapi.tiangolo.com", None),
    "pydantic": ("https://docs.pydantic.dev", None),
    "sqlalchemy": ("https://docs.sqlalchemy.org/en/20/", None),
}
```

## Additional Best Practices

### 1. Database Migrations (Alembic)
```python
# alembic/env.py - Key configuration
from my_app.db.base import Base
from my_app.core.config import settings

target_metadata = Base.metadata
config.set_main_option("sqlalchemy.url", str(settings.database_url))
```

### 2. Dependency Injection Pattern
```python
# src/my_app/api/deps.py
"""Common dependencies for API endpoints."""

from typing import AsyncGenerator
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from my_app.db.session import get_db
from my_app.core.security import verify_token


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(verify_token),
) -> User:
    """Get current authenticated user."""
    # Implementation here
    pass
```

### 3. Input Validation
```python
# src/my_app/utils/validators.py
"""Custom validation functions."""

import re
from typing import Any

from pydantic import validator


def validate_email(email: str) -> str:
    """Validate email format.
    
    Args:
        email: Email address to validate
        
    Returns:
        str: Validated email address
        
    Raises:
        ValueError: If email format is invalid
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, email):
        raise ValueError("Invalid email format")
    return email.lower()


def validate_password_strength(password: str) -> str:
    """Validate password strength.
    
    Args:
        password: Password to validate
        
    Returns:
        str: Validated password
        
    Raises:
        ValueError: If password doesn't meet requirements
    """
    if len(password) < 8:
        raise ValueError("Password must be at least 8 characters long")
    
    if not re.search(r'[A-Z]', password):
        raise ValueError("Password must contain at least one uppercase letter")
    
    if not re.search(r'[a-z]', password):
        raise ValueError("Password must contain at least one lowercase letter")
    
    if not re.search(r'\d', password):
        raise ValueError("Password must contain at least one digit")
    
    return password
```

### 4. Logging Configuration
```python
# src/my_app/core/logging.py
"""Structured logging configuration."""

import logging
import sys
from typing import Any, Dict

import structlog
from rich.console import Console
from rich.logging import RichHandler

from my_app.core.config import settings


def setup_logging() -> None:
    """Configure structured logging with rich output."""
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        datefmt="[%X]",
        handlers=[
            RichHandler(
                console=Console(stderr=True),
                rich_tracebacks=True,
                tracebacks_show_locals=settings.debug,
            )
        ],
        level=getattr(logging, settings.log_level.upper()),
        force=True,
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.add_log_level,
            structlog.processors.StackInfoRenderer(),
            structlog.dev.set_exc_info,
            structlog.processors.TimeStamper(fmt="iso"),
        ]
        + (
            [structlog.dev.ConsoleRenderer()]
            if not settings.is_production
            else [structlog.processors.JSONRenderer()]
        ),
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.log_level.upper())
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a configured logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)
```

### 5. Security Best Practices
```python
# src/my_app/core/security.py
"""Security utilities and authentication."""

from datetime import datetime, timedelta
from typing import Any, Optional, Union

from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

from my_app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()


def create_access_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None,
) -> str:
    """Create JWT access token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        
    Returns:
        str: JWT token
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.access_token_expire_minutes
        )
    
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.secret_key,
        algorithm=settings.algorithm,
    )
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        bool: True if password is correct
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password.
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


async def verify_token(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> str:
    """Verify JWT token and extract subject.
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        str: Token subject
        
    Raises:
        HTTPException: If token is invalid
    """
    try:
        payload = jwt.decode(
            credentials.credentials,
            settings.secret_key,
            algorithms=[settings.algorithm],
        )
        subject: str = payload.get("sub")
        if subject is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
            )
        return subject
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
        )
```

## Additional Robustness Recommendations

### 1. Performance Optimizations
- Use connection pooling with proper pool sizes
- Implement database query optimization with indexes
- Add Redis for caching frequently accessed data
- Use async/await throughout the application
- Implement database read replicas for read-heavy operations

### 2. Monitoring & Observability
- Add Prometheus metrics with `prometheus_fastapi_instrumentator`
- Implement distributed tracing with OpenTelemetry
- Set up health checks for dependencies (database, external APIs)
- Add application performance monitoring (APM)

### 3. Security Hardening
- Implement rate limiting with `slowapi`
- Add request ID tracking for better debugging
- Use proper HTTPS certificates in production
- Implement proper CORS policies
- Add input sanitization for XSS prevention
- Use dependency scanning in CI/CD

### 4. Infrastructure
- Use Docker multi-stage builds for smaller images
- Implement graceful shutdown handling
- Add readiness and liveness probes for Kubernetes
- Use environment-specific configuration management
- Implement backup and disaster recovery procedures

### 5. Code Quality
- Achieve >90% test coverage
- Use mutation testing with `mutmut`
- Implement contract testing for APIs
- Add integration tests for critical user flows
- Use property-based testing with `hypothesis`

This guide provides a solid foundation for building robust, maintainable FastAPI applications that follow Python best practices and industry standards.